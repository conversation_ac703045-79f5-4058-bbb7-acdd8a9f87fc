import useChatInfo from '@/hooks/useChatInfo'
import styles from './index.module.scss'
import FsAvatarProps from '@/component/FsAvatar'
import { useEffect, useState, useRef, useCallback } from 'react'
import draftIcon from '@/assets/image/draftBox.svg'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@renderer/store'
import { updateUserFormData } from '@renderer/store/modules/message'
import { ChatTimer } from '@/utils/chatTimer'
import eventBus from '@/utils/eventBus'
interface ListProps {
  itemData: MessageItemProps
}
//联系人列表项
export default function ListItem(props: ListProps) {
  const dispatch = useDispatch()
  const { itemData } = props
  const [active, setActive] = useState(false)
  const [chatDuration, setChatDuration] = useState('')
  const { chatRoomInfo, setChatInfo, resetMessageCountMap, setChatStatus } =
    useChatInfo()
  const draftBox = useSelector((state: RootState) => state.chat.draftBox)
  const timerRef = useRef<ChatTimer | null>(null)
  const clickChatRoom = () => {
    if (itemData.groupId === chatRoomInfo.groupId) return
    itemData?.isFinished
      ? setChatStatus(itemData.isFinished)
      : setChatStatus(false)
    setChatInfo({ groupId: itemData.groupId })
    resetMessageCountMap(itemData.groupId)
    dispatch(updateUserFormData({}))
  }
  // 初始化计时器
  const initChatTimer = useCallback(() => {
    if (itemData.effectiveTime) {
      // 销毁之前的计时器
      timerRef.current?.destroy()

      // 创建新的计时器
      timerRef.current = new ChatTimer({
        effectiveTime: itemData.effectiveTime,
        onUpdate: setChatDuration
      })

      // 开始计时
      timerRef.current.start()
    } else {
      setChatDuration('')
    }
  }, [itemData.effectiveTime])



  useEffect(() => {
    itemData.groupId === chatRoomInfo.groupId
      ? setActive(true)
      : setActive(false)
  }, [chatRoomInfo.groupId])

  // 初始化计时器
  useEffect(() => {
    initChatTimer()

    // 清理函数
    return () => {
      timerRef.current?.destroy()
    }
  }, [initChatTimer])

  // 监听消息接收事件
  useEffect(() => {
    console.log('注册receiveMsg监听器，groupId:', itemData.groupId, '监听器总数:', eventBus.getListenerCount('receiveMsg'));

    // 创建一个稳定的处理函数，避免频繁注册
    const messageHandler = (msg: MessageItemProps) => {
      console.log('收到消息事件：', {
        msgGroupId: msg.groupId,
        currentGroupId: itemData.groupId,
        messageType: msg.messageType,
        isMatch: msg.groupId === itemData.groupId
      });

      // 只处理当前聊天室的消息
      if (msg.groupId !== itemData.groupId) {
        return;
      }

      // 客户消息 (messageType === 32) 或自己发送的消息 (messageType === 2 或 21)
      if (msg.messageType === 32 || msg.messageType === 2 || msg.messageType === 21) {
        console.log('符合条件，更新计时器:', msg.messageType, '当前时间:', new Date().toLocaleTimeString());
        const newEffectiveTime = Date.now()
        if (timerRef.current) {
          timerRef.current.updateEffectiveTime(newEffectiveTime)
          console.log('计时器已更新');
        } else {
          // 如果计时器不存在，创建新的计时器
          timerRef.current = new ChatTimer({
            effectiveTime: newEffectiveTime,
            onUpdate: setChatDuration
          })
          timerRef.current.start()
          console.log('新计时器已创建并启动');
        }
      }
    }

    // 使用改进的事件监听，返回取消订阅函数
    const unsubscribe = eventBus.on('receiveMsg', messageHandler)
    console.log('监听器注册完成，当前监听器总数:', eventBus.getListenerCount('receiveMsg'));

    return () => {
      console.log('组件卸载，移除监听器，groupId:', itemData.groupId);
      // 使用返回的取消订阅函数，只移除当前组件的监听器
      unsubscribe()
      console.log('监听器移除完成，剩余监听器总数:', eventBus.getListenerCount('receiveMsg'));
    }
  }, [itemData.groupId]) // 只依赖 groupId，避免频繁重新注册
  return (
    <div className={styles.list_item_wrapper} onClick={clickChatRoom}>
      <div
        className={`${styles.list_item_content} ${active ? styles.active : ''}`}
      >
        <FsAvatarProps
          style={{ marginRight: '8px' }}
          name={itemData.name}
          onlineStatus={itemData.onlineStatus}
          messageCount={itemData.messageCount}
          backgroundColor={itemData.avatar}
        ></FsAvatarProps>
        <div className={styles.list_item_info}>
          <div className={styles.list_item_name_time}>
            <div className={styles.list_item_name}>{itemData.name}</div>
            <div className={styles.list_item_time_wrapper}>
              <div className={styles.list_item_time}>{itemData.messageTime}</div>
              {chatDuration && (
                <div className={styles.list_item_duration}>{chatDuration}</div>
              )}
            </div>
          </div>
          <div className={styles.list_item_message}>
            {draftBox[itemData.groupId] &&
              chatRoomInfo.groupId !== itemData.groupId && (
                <img className={styles.draft_icon} src={draftIcon}></img>
              )}
            {itemData.msg}
          </div>
        </div>
      </div>
    </div>
  )
}
